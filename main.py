"""
金融股指期货监控系统主程序
"""
import asyncio
import logging
import sys
import os
from datetime import datetime
import pandas as pd
import warnings

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.models.data_models import MonitoringConfig
from src.data_providers.efinance_provider import EfinanceProvider
from src.data_providers.index_provider import IndexDataProvider
from src.utils.trading_calendar import TradingCalendar
from src.calculators.basis_calculator import BasisCalculator
from src.calculators.spread_calculator import SpreadCalculator
from src.analyzers.cost_matrix import CostMatrixGenerator

pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.width', 180)
warnings.filterwarnings('ignore') 



class FuturesMonitoringSystem:
    """期货监控系统主类"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.logger = self._setup_logging()
        
        # 初始化组件
        self.efinance_provider = EfinanceProvider()
        self.index_provider = IndexDataProvider()
        self.trading_calendar = TradingCalendar()
        self.basis_calculator = BasisCalculator(self.trading_calendar, config.trading_days_per_year)
        self.spread_calculator = SpreadCalculator(self.trading_calendar, config.trading_days_per_year)
        self.cost_matrix_generator = CostMatrixGenerator()
        
        self.is_running = False
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('futures_monitor.log', encoding='utf-8')
            ]
        )
        return logging.getLogger(__name__)
    
    async def start(self):
        """启动监控系统"""
        self.is_running = True
        self.logger.info("=== 金融股指期货监控系统启动 ===")
        
        try:
            # 验证数据源
            await self._validate_data_sources()
            
            # 主监控循环
            await self._monitoring_loop()
            
        except KeyboardInterrupt:
            self.logger.info("收到停止信号")
        except Exception as e:
            self.logger.error(f"系统运行错误: {e}")
        finally:
            await self.stop()
    
    async def stop(self):
        """停止监控系统"""
        self.is_running = False
        self.logger.info("正在停止监控系统...")
        
        # 关闭资源
        await self.efinance_provider.close()
        await self.index_provider.close()
        await self.trading_calendar.close()
        
        self.logger.info("监控系统已停止")
    
    async def _validate_data_sources(self):
        """验证数据源可用性"""
        self.logger.info("验证数据源可用性...")
        
        # 验证指数数据源
        status = await self.index_provider.validate_data_sources()
        self.logger.info(f"数据源状态: {status}")
        
        if not any(status.values()):
            raise Exception("所有数据源都不可用")
    
    async def _monitoring_loop(self):
        """主监控循环"""
        while self.is_running:
            try:
                self.logger.info(f"开始数据更新 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 1. 获取期货合约信息
                product_codes = list(self.config.futures_mapping.keys())
                contracts = await self.efinance_provider.get_futures_contracts(product_codes)
                
                if not contracts:
                    self.logger.warning("未获取到期货合约信息")
                    await asyncio.sleep(30)
                    continue
                
                self.logger.info(f"获取到 {len(contracts)} 个期货合约")
                
                # 2. 获取期货行情
                contract_codes = [c.contract_code for c in contracts]
                futures_quotes = await self.efinance_provider.get_futures_quotes(contract_codes)
                
                if not futures_quotes:
                    self.logger.warning("未获取到期货行情")
                    await asyncio.sleep(30)
                    continue
                
                # 3. 获取指数行情
                index_codes = list(self.config.futures_mapping.values())
                index_quotes = await self.index_provider.get_index_quotes(index_codes)
                
                if not index_quotes:
                    self.logger.warning("未获取到指数行情")
                    await asyncio.sleep(30)
                    continue
                
                # 4. 计算基差成本
                basis_costs = await self.basis_calculator.calculate_multiple_basis(
                    futures_quotes, index_quotes, contracts, self.config.futures_mapping
                )
                
                # 5. 计算价差成本
                spread_costs = await self.spread_calculator.calculate_all_spreads(
                    futures_quotes, contracts
                )
                
                # 6. 生成成本矩阵
                cost_matrix = self.cost_matrix_generator.generate_cost_matrix(
                    basis_costs, spread_costs, contracts
                )
                
                # 7. 显示结果
                await self._display_results(cost_matrix, contracts)
                
                # 8. 等待下次更新
                self.logger.info(f"等待 {self.config.update_interval} 秒后下次更新...")
                await asyncio.sleep(self.config.update_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                await asyncio.sleep(10)
    
    async def _display_results(self, cost_matrix, contracts):
        """显示结果"""
        print("\n" + "="*80)
        print(f"金融股指期货监控面板 - 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        # 显示基差成本矩阵
        if cost_matrix.basis_costs:
            print("\n【基差成本矩阵】")
            basis_df = self.cost_matrix_generator.create_basis_matrix_dataframe(
                cost_matrix.basis_costs, contracts
            )
            print(basis_df.to_string(index=False))
        
        # 显示价差成本矩阵
        if cost_matrix.spread_costs:
            print("\n【价差成本矩阵】")
            spread_df = self.cost_matrix_generator.create_spread_matrix_dataframe(
                cost_matrix.spread_costs, contracts
            )
            print(spread_df.to_string(index=False))
        
        # 显示汇总统计
        summary = self.cost_matrix_generator.generate_summary_statistics(cost_matrix)
        print(f"\n【汇总统计】")
        print(f"基差机会数量: {summary['overall_summary']['basis_opportunities']}")
        print(f"价差机会数量: {summary['overall_summary']['spread_opportunities']}")
        
        if summary['basis_summary']:
            print(f"平均基差率: {summary['basis_summary']['avg_basis_rate']:.2f}%")
            print(f"平均年化成本: {summary['basis_summary']['avg_annualized_cost']:.2f}%")
        
        if summary['spread_summary']:
            print(f"平均价差率: {summary['spread_summary']['avg_spread_rate']:.2f}%")
            print(f"平均时间成本: {summary['spread_summary']['avg_time_cost']:.2f}%")


async def main():
    """主函数"""
    # 创建配置
    config = MonitoringConfig(
        futures_mapping={
            'IC': 'sh000905',  # 中证500股指期货 -> 中证500指数
            'IM': 'sh000852',  # 中证1000股指期货 -> 中证1000指数
            'IF': 'sh000300',  # 沪深300股指期货 -> 沪深300指数
            'IH': 'sh000016',  # 上证50股指期货 -> 上证50指数
        },
        trading_days_per_year=243,
        update_interval=60,  # 60秒更新一次
        log_level="INFO"
    )
    
    # 创建并启动监控系统
    system = FuturesMonitoringSystem(config)
    await system.start()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序已停止")
    except Exception as e:
        print(f"程序运行错误: {e}")
        import traceback
        traceback.print_exc()
