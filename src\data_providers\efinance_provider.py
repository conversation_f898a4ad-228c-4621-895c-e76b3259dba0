"""
基于efinance API的数据提供者实现
"""
import asyncio
import logging
from datetime import datetime, date
from typing import Dict, List, Optional
import pandas as pd
import efinance as ef
from concurrent.futures import ThreadPoolExecutor

from ..models.data_models import FuturesContract, FuturesQuote, IndexQuote
from ..core.architecture import DataProvider


class EfinanceProvider:
    """efinance数据提供者"""
    
    def __init__(self, max_workers: int = 4):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.logger = logging.getLogger(__name__)
        
        # 缓存期货基础信息
        self._futures_base_info: Optional[pd.DataFrame] = None
        self._last_base_info_update: Optional[datetime] = None
        self._base_info_cache_duration = 3600  # 1小时缓存
    
    async def get_futures_quotes(self, contracts: List[str]) -> Dict[str, FuturesQuote]:
        """获取期货行情"""
        try:
            # 在线程池中执行同步API调用
            quotes_df = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.futures.get_realtime_quotes
            )
            
            result = {}
            for contract_code in contracts:
                # 查找对应的行情数据
                contract_data = quotes_df[quotes_df['期货代码'] == contract_code]
                if not contract_data.empty:
                    row = contract_data.iloc[0]
                    
                    quote = FuturesQuote(
                        contract_code=contract_code,
                        timestamp=datetime.now(),
                        current_price=float(row.get('最新价', 0)),
                        open_price=float(row.get('今开', 0)),
                        high_price=float(row.get('最高', 0)),
                        low_price=float(row.get('最低', 0)),
                        volume=int(row.get('成交量', 0)),
                        amount=float(row.get('成交额', 0)),
                        prev_close=float(row.get('昨日收盘', 0)),
                        change_pct=float(row.get('涨跌幅', 0)),
                        change_amount=float(row.get('涨跌额', 0))
                    )
                    result[contract_code] = quote
                    
            self.logger.info(f"获取到 {len(result)} 个期货合约行情")
            return result
            
        except Exception as e:
            self.logger.error(f"获取期货行情失败: {e}")
            return {}
    
    async def get_index_quotes(self, indices: List[str]) -> Dict[str, IndexQuote]:
        """获取指数行情"""
        try:
            # 在线程池中执行同步API调用
            quotes_df = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.stock.get_realtime_quotes, indices
            )
            
            result = {}
            for _, row in quotes_df.iterrows():
                index_code = row.get('代码', '')
                if index_code in indices:
                    quote = IndexQuote(
                        index_code=index_code,
                        index_name=row.get('名称', ''),
                        timestamp=datetime.now(),
                        current_price=float(row.get('最新价', 0)),
                        open_price=float(row.get('今开', 0)),
                        high_price=float(row.get('最高', 0)),
                        low_price=float(row.get('最低', 0)),
                        volume=int(row.get('成交量', 0)),
                        amount=float(row.get('成交额', 0)),
                        prev_close=float(row.get('昨收', 0)),
                        change_pct=float(row.get('涨跌幅', 0)),
                        change_amount=float(row.get('涨跌额', 0))
                    )
                    result[index_code] = quote
                    
            self.logger.info(f"获取到 {len(result)} 个指数行情")
            return result
            
        except Exception as e:
            self.logger.error(f"获取指数行情失败: {e}")
            return {}
    
    async def get_futures_contracts(self, product_codes: List[str]) -> List[FuturesContract]:
        """获取期货合约信息"""
        try:
            # 获取基础信息
            base_info = await self._get_futures_base_info()
            if base_info is None:
                return []
            
            # 获取实时行情以筛选活跃合约
            quotes_df = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.futures.get_realtime_quotes
            )
            
            # 添加品种代码列
            quotes_df['product_code'] = quotes_df['期货代码'].str.extract(r'([A-Za-z]+)')
            
            contracts = []
            for product_code in product_codes:
                # 筛选该品种的合约
                product_quotes = quotes_df[quotes_df['product_code'] == product_code].copy()
                
                if not product_quotes.empty:
                    # 按成交量排序，获取最活跃的合约
                    product_quotes = product_quotes.sort_values('成交量', ascending=False)
                    
                    # 取前6个最活跃的合约
                    top_contracts = product_quotes.head(6)
                    
                    for idx, row in top_contracts.iterrows():
                        contract_code = row['期货代码']
                        
                        # 从基础信息中获取详细信息
                        base_row = base_info[base_info['期货代码'] == contract_code]
                        
                        quote_id = row.get('行情ID', '')
                        market_type = row.get('市场类型', '')
                        
                        if not base_row.empty:
                            quote_id = base_row.iloc[0].get('行情ID', quote_id)
                            market_type = base_row.iloc[0].get('市场类型', market_type)
                        
                        # 判断是否为主力合约（成交量最大的）
                        is_main = idx == top_contracts.index[0]
                        
                        # 解析到期日期（从合约代码推断）
                        expiry_date = self._parse_expiry_date(contract_code)
                        
                        contract = FuturesContract(
                            contract_code=contract_code,
                            product_code=product_code,
                            contract_name=row.get('期货名称', ''),
                            quote_id=quote_id,
                            market_type=market_type,
                            expiry_date=expiry_date,
                            is_main_contract=is_main
                        )
                        contracts.append(contract)
            
            self.logger.info(f"获取到 {len(contracts)} 个期货合约")
            return contracts
            
        except Exception as e:
            self.logger.error(f"获取期货合约信息失败: {e}")
            return []
    
    async def get_trading_days(self, start_date: str, end_date: str) -> List[str]:
        """获取交易日历"""
        try:
            trading_days = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.trade_calendar.get_trade_days, start_date, end_date
            )
            return trading_days
            
        except Exception as e:
            self.logger.error(f"获取交易日历失败: {e}")
            return []
    
    async def get_quote_history(self, quote_id: str, days: int = 30) -> pd.DataFrame:
        """获取历史行情数据"""
        try:
            from datetime import timedelta
            
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
            
            history_df = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.futures.get_quote_history, quote_id, start_date, end_date
            )
            
            return history_df
            
        except Exception as e:
            self.logger.error(f"获取历史行情失败: {e}")
            return pd.DataFrame()
    
    async def _get_futures_base_info(self) -> Optional[pd.DataFrame]:
        """获取期货基础信息（带缓存）"""
        now = datetime.now()
        
        # 检查缓存是否有效
        if (self._futures_base_info is not None and 
            self._last_base_info_update is not None and
            (now - self._last_base_info_update).seconds < self._base_info_cache_duration):
            return self._futures_base_info
        
        try:
            # 重新获取基础信息
            base_info = await asyncio.get_event_loop().run_in_executor(
                self.executor, ef.futures.get_futures_base_info
            )
            
            self._futures_base_info = base_info
            self._last_base_info_update = now
            
            self.logger.info("更新期货基础信息缓存")
            return base_info
            
        except Exception as e:
            self.logger.error(f"获取期货基础信息失败: {e}")
            return None
    
    def _parse_expiry_date(self, contract_code: str) -> Optional[date]:
        """从合约代码解析到期日期"""
        try:
            # 提取月份代码，如IC2407中的2407
            if len(contract_code) >= 6:
                month_code = contract_code[-4:]  # 如2407
                year = int("20" + month_code[:2])  # 2024
                month = int(month_code[2:])  # 07
                
                # 股指期货通常在第三个周五到期
                # 这里简化处理，设为当月第三个周五
                from calendar import monthrange
                import calendar
                
                # 找到当月第三个周五
                first_day_weekday = calendar.weekday(year, month, 1)
                first_friday = 1 + (4 - first_day_weekday) % 7
                third_friday = first_friday + 14
                
                # 确保不超过当月天数
                days_in_month = monthrange(year, month)[1]
                if third_friday > days_in_month:
                    third_friday -= 7
                
                return date(year, month, third_friday)
                
        except Exception as e:
            self.logger.warning(f"解析到期日期失败 {contract_code}: {e}")
        
        return None
    
    async def close(self):
        """关闭资源"""
        self.executor.shutdown(wait=True)
