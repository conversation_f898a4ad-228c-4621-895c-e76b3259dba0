"""
测试efinance API的指数数据获取
"""
import efinance as ef
import pandas as pd

def test_index_quotes():
    """测试指数行情获取"""
    print("=== 测试指数行情获取 ===")
    
    # 测试不同的指数代码格式
    test_codes = [
        ['000300'],  # 沪深300
        ['000905'],  # 中证500
        ['000852'],  # 中证1000
        ['000016'],  # 上证50
        ['sh000300'],  # 带sh前缀
        ['sz399001'],  # 深证成指
    ]
    
    for codes in test_codes:
        try:
            print(f"\n测试代码: {codes}")
            df = ef.stock.get_realtime_quotes(codes)
            print(f"成功获取 {len(df)} 条数据")
            if not df.empty:
                print(df[['代码', '名称', '最新价']].head())
        except Exception as e:
            print(f"失败: {e}")

def test_futures_quotes():
    """测试期货行情获取"""
    print("\n=== 测试期货行情获取 ===")

    try:
        df = ef.futures.get_realtime_quotes()
        print(f"获取到 {len(df)} 个期货合约")

        # 查看市场类型
        print("\n市场类型分布:")
        print(df['市场类型'].value_counts())

        # 查找中金所的合约
        cffex_contracts = df[df['市场类型'] == '中金所']
        print(f"\n中金所合约数量: {len(cffex_contracts)}")

        if not cffex_contracts.empty:
            print("中金所合约:")
            print(cffex_contracts[['期货代码', '期货名称', '最新价', '成交量']].head(20))

        # 查找名称中包含股指的
        stock_index_by_name = df[df['期货名称'].str.contains('股指|沪深|中证|上证|50|300|500|1000', na=False)]
        if not stock_index_by_name.empty:
            print(f"\n按名称筛选的股指期货 ({len(stock_index_by_name)} 个):")
            print(stock_index_by_name[['期货代码', '期货名称', '最新价', '成交量']].head(10))
        else:
            print("\n未找到股指期货相关合约")

    except Exception as e:
        print(f"获取期货行情失败: {e}")

def test_futures_base_info():
    """测试期货基础信息"""
    print("\n=== 测试期货基础信息 ===")

    try:
        df = ef.futures.get_futures_base_info()
        print(f"获取到 {len(df)} 个期货基础信息")
        print(f"列名: {df.columns.tolist()}")

        # 查找中金所
        cffex_info = df[df['市场类型'] == '中金所']
        print(f"\n中金所期货数量: {len(cffex_info)}")

        if not cffex_info.empty:
            print("中金所期货:")
            print(cffex_info.head(20))

    except Exception as e:
        print(f"获取期货基础信息失败: {e}")

if __name__ == "__main__":
    test_index_quotes()
    test_futures_quotes()
    test_futures_base_info()
